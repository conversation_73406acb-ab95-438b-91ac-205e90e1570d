import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { logout } from '../store/slices/authSlice';
import { COLORS, SIZES } from '../utils/constants';

const ProfileScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const { user, walletAddress, isAuthenticated } = useSelector(state => state.auth);
  const { profile } = useSelector(state => state.user);

  const handleLogout = () => {
    Alert.alert(
      '確認登出',
      '您確定要登出嗎？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '登出',
          style: 'destructive',
          onPress: () => {
            dispatch(logout());
            navigation.replace('Auth');
          },
        },
      ]
    );
  };

  const navigateToWallet = () => {
    navigation.navigate('Wallet');
  };

  const formatAddress = (address) => {
    if (!address) return '未連接';
    return `${address.slice(0, 8)}...${address.slice(-8)}`;
  };

  return (
    <ScrollView style={styles.container}>
      {/* 用戶基本資訊 */}
      <View style={styles.profileCard}>
        <View style={styles.avatar}>
          <Text style={styles.avatarText}>
            {user?.username ? user.username.charAt(0).toUpperCase() : '?'}
          </Text>
        </View>

        <Text style={styles.username}>
          {user?.username || '未設置暱稱'}
        </Text>

        <Text style={styles.email}>
          {user?.email || '未設置郵箱'}
        </Text>
      </View>

      {/* 錢包資訊 */}
      <View style={styles.walletCard}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>錢包資訊</Text>
          <TouchableOpacity
            style={styles.manageButton}
            onPress={navigateToWallet}
          >
            <Text style={styles.manageButtonText}>管理</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.walletInfo}>
          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>連接狀態</Text>
            <View style={[
              styles.statusBadge,
              walletAddress ? styles.statusConnected : styles.statusDisconnected
            ]}>
              <Text style={styles.statusText}>
                {walletAddress ? '已連接' : '未連接'}
              </Text>
            </View>
          </View>

          <View style={styles.infoRow}>
            <Text style={styles.infoLabel}>錢包地址</Text>
            <Text style={styles.infoValue}>
              {formatAddress(walletAddress)}
            </Text>
          </View>
        </View>
      </View>

      {/* 遊戲統計 */}
      <View style={styles.statsCard}>
        <Text style={styles.cardTitle}>遊戲統計</Text>

        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {user?.gameStats?.totalGames || 0}
            </Text>
            <Text style={styles.statLabel}>總場次</Text>
          </View>

          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {user?.gameStats?.wins || 0}
            </Text>
            <Text style={styles.statLabel}>勝利</Text>
          </View>

          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {user?.gameStats?.losses || 0}
            </Text>
            <Text style={styles.statLabel}>失敗</Text>
          </View>

          <View style={styles.statItem}>
            <Text style={styles.statValue}>
              {user?.gameStats?.rating || 1200}
            </Text>
            <Text style={styles.statLabel}>積分</Text>
          </View>
        </View>
      </View>

      {/* 操作按鈕 */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={styles.walletButton}
          onPress={navigateToWallet}
        >
          <Text style={styles.walletButtonText}>錢包管理</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.logoutButton}
          onPress={handleLogout}
        >
          <Text style={styles.logoutButtonText}>登出</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  profileCard: {
    backgroundColor: COLORS.SURFACE,
    margin: 16,
    padding: 24,
    borderRadius: SIZES.BORDER_RADIUS,
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  avatarText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  username: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 4,
  },
  email: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
  },
  walletCard: {
    backgroundColor: COLORS.SURFACE,
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: SIZES.BORDER_RADIUS,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  manageButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  manageButtonText: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  walletInfo: {
    gap: 12,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoLabel: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusConnected: {
    backgroundColor: COLORS.SUCCESS + '20',
  },
  statusDisconnected: {
    backgroundColor: COLORS.ERROR + '20',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  statsCard: {
    backgroundColor: COLORS.SURFACE,
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: SIZES.BORDER_RADIUS,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 16,
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
  },
  actionButtons: {
    margin: 16,
    marginTop: 0,
    gap: 12,
  },
  walletButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: 14,
    borderRadius: SIZES.BORDER_RADIUS,
    alignItems: 'center',
  },
  walletButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  logoutButton: {
    backgroundColor: COLORS.ERROR,
    paddingVertical: 14,
    borderRadius: SIZES.BORDER_RADIUS,
    alignItems: 'center',
  },
  logoutButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
});

export default ProfileScreen;
