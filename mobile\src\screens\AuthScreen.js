import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { loginWithWallet } from '../store/slices/authSlice';
import { COLORS, SIZES } from '../utils/constants';

const AuthScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const { loading, error } = useSelector(state => state.auth);
  const [isConnecting, setIsConnecting] = useState(false);

  // 快速錢包登入
  const handleWalletLogin = async () => {
    try {
      setIsConnecting(true);
      const result = await dispatch(loginWithWallet());

      if (result.type === 'auth/loginWithWallet/fulfilled') {
        navigation.replace('Home');
      } else {
        Alert.alert('錢包登入失敗', result.payload || '未知錯誤');
      }
    } catch (error) {
      Alert.alert('錢包登入失敗', error.message);
    } finally {
      setIsConnecting(false);
    }
  };

  // 導航到詳細登入頁面
  const navigateToLogin = () => {
    navigation.navigate('Login');
  };

  // 導航到註冊頁面
  const navigateToRegister = () => {
    navigation.navigate('Register');
  };

  return (
    <View style={styles.container}>
      {/* Logo 區域 */}
      <View style={styles.logoContainer}>
        <View style={styles.logoPlaceholder}>
          <Text style={styles.logoText}>ChainXiang</Text>
          <Text style={styles.logoSubtext}>鏈象</Text>
        </View>
        <Text style={styles.tagline}>區塊鏈象棋對戰平台</Text>
      </View>

      {/* 登入區域 */}
      <View style={styles.loginContainer}>
        <Text style={styles.title}>歡迎來到 ChainXiang</Text>
        <Text style={styles.subtitle}>
          選擇您的登入方式，開始象棋之旅
        </Text>

        {/* 快速錢包登入 */}
        <TouchableOpacity
          style={[
            styles.walletButton,
            (loading || isConnecting) && styles.walletButtonDisabled,
          ]}
          onPress={handleWalletLogin}
          disabled={loading || isConnecting}
        >
          {loading || isConnecting ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator color={COLORS.TEXT_PRIMARY} size="small" />
              <Text style={styles.walletButtonText}>連接中...</Text>
            </View>
          ) : (
            <>
              <View style={styles.walletIcon}>
                <Text style={styles.walletIconText}>👛</Text>
              </View>
              <Text style={styles.walletButtonText}>快速錢包登入</Text>
            </>
          )}
        </TouchableOpacity>

        {/* 其他登入選項 */}
        <View style={styles.otherOptions}>
          <TouchableOpacity
            style={styles.optionButton}
            onPress={navigateToLogin}
          >
            <Text style={styles.optionButtonText}>使用帳號登入</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.optionButton, styles.registerButton]}
            onPress={navigateToRegister}
          >
            <Text style={[styles.optionButtonText, styles.registerButtonText]}>
              創建新帳號
            </Text>
          </TouchableOpacity>
        </View>

        {/* 支援的錢包 */}
        <View style={styles.supportedWallets}>
          <Text style={styles.supportedWalletsTitle}>支援的錢包：</Text>
          <View style={styles.walletList}>
            <View style={styles.walletItem}>
              <Text style={styles.walletName}>Phantom</Text>
            </View>
            <View style={styles.walletItem}>
              <Text style={styles.walletName}>Solflare</Text>
            </View>
            <View style={styles.walletItem}>
              <Text style={styles.walletName}>Backpack</Text>
            </View>
          </View>
        </View>

        {/* 錯誤訊息 */}
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}
      </View>

      {/* 底部資訊 */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          首次使用錢包登入將自動創建帳號
        </Text>
        <Text style={styles.versionText}>v1.0.0</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 60,
  },
  logoPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: COLORS.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    elevation: 8,
    shadowColor: COLORS.PRIMARY,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  logoText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  logoSubtext: {
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    marginTop: 4,
  },
  tagline: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  loginContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 24,
  },
  walletButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: SIZES.BORDER_RADIUS,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 4,
    shadowColor: COLORS.PRIMARY,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    marginBottom: 30,
    minWidth: 200,
  },
  walletButtonDisabled: {
    backgroundColor: COLORS.TEXT_SECONDARY,
    elevation: 0,
    shadowOpacity: 0,
  },
  walletIcon: {
    marginRight: 12,
  },
  walletIconText: {
    fontSize: 24,
  },
  walletButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  otherOptions: {
    marginTop: 24,
    marginBottom: 32,
  },
  optionButton: {
    backgroundColor: COLORS.SURFACE,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    paddingVertical: 12,
    borderRadius: SIZES.BORDER_RADIUS,
    alignItems: 'center',
    marginBottom: 12,
  },
  registerButton: {
    backgroundColor: COLORS.SECONDARY,
    borderColor: COLORS.SECONDARY,
  },
  optionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
  },
  registerButtonText: {
    fontWeight: '600',
  },
  supportedWallets: {
    alignItems: 'center',
    marginBottom: 20,
  },
  supportedWalletsTitle: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 12,
  },
  walletList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  walletItem: {
    backgroundColor: COLORS.SURFACE,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginHorizontal: 4,
    marginVertical: 2,
  },
  walletName: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
  },
  errorContainer: {
    backgroundColor: COLORS.ERROR,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: SIZES.BORDER_RADIUS,
    marginTop: 20,
  },
  errorText: {
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    fontSize: 14,
  },
  footer: {
    alignItems: 'center',
    marginTop: 20,
  },
  footerText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: 8,
  },
  versionText: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    opacity: 0.6,
  },
});

export default AuthScreen;
