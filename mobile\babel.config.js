module.exports = {
  presets: [
    'module:metro-react-native-babel-preset',
    ['@babel/preset-env', {
      targets: {
        node: 'current',
      },
    }],
  ],
  plugins: [
    // React Native Reanimated plugin (必須是最後一個)
    'react-native-reanimated/plugin',
  ],
  env: {
    test: {
      presets: [
        'module:metro-react-native-babel-preset',
        ['@babel/preset-env', {
          targets: {
            node: 'current',
          },
        }],
      ],
      plugins: [
        // 測試環境下的插件
      ],
    },
  },
};
