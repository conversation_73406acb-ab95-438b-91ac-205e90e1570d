import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { useSelector } from 'react-redux';
import { COLORS, SIZES } from '../utils/constants';

const WalletStatus = ({ onPress, style }) => {
  const { walletAddress, user } = useSelector(state => state.auth);

  const formatAddress = (address) => {
    if (!address) return '未連接';
    return `${address.slice(0, 6)}...${address.slice(-6)}`;
  };

  const isConnected = !!walletAddress;

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>錢包狀態</Text>
          <View style={[
            styles.statusIndicator,
            isConnected ? styles.statusConnected : styles.statusDisconnected
          ]}>
            <Text style={styles.statusText}>
              {isConnected ? '已連接' : '未連接'}
            </Text>
          </View>
        </View>
        
        <Text style={styles.address}>
          {formatAddress(walletAddress)}
        </Text>
        
        {isConnected && user?.walletConnected && (
          <Text style={styles.hint}>點擊管理錢包</Text>
        )}
      </View>
      
      <View style={styles.arrow}>
        <Text style={styles.arrowText}>›</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  statusIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusConnected: {
    backgroundColor: COLORS.SUCCESS + '20',
  },
  statusDisconnected: {
    backgroundColor: COLORS.ERROR + '20',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  address: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 4,
  },
  hint: {
    fontSize: 12,
    color: COLORS.PRIMARY,
    fontStyle: 'italic',
  },
  arrow: {
    marginLeft: 12,
  },
  arrowText: {
    fontSize: 20,
    color: COLORS.TEXT_SECONDARY,
  },
});

export default WalletStatus;
