// API 服務測試
import { apiService } from '../services/api';

// 模擬 AsyncStorage
const mockAsyncStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};

jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

// 模擬 fetch
global.fetch = jest.fn();

describe('API Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockAsyncStorage.getItem.mockResolvedValue(null);
  });

  describe('getAuthHeaders', () => {
    test('應該返回基本標頭當沒有 token 時', async () => {
      mockAsyncStorage.getItem.mockResolvedValue(null);
      
      const headers = await apiService.getAuthHeaders();
      
      expect(headers).toEqual({
        'Content-Type': 'application/json',
      });
      expect(mockAsyncStorage.getItem).toHaveBeenCalledWith('auth_token');
    });

    test('應該包含 Authorization 標頭當有 token 時', async () => {
      const testToken = 'test-jwt-token';
      mockAsyncStorage.getItem.mockResolvedValue(testToken);
      
      const headers = await apiService.getAuthHeaders();
      
      expect(headers).toEqual({
        'Content-Type': 'application/json',
        Authorization: `Bearer ${testToken}`,
      });
    });
  });

  describe('request', () => {
    test('應該成功處理 API 請求', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({ success: true, data: 'test' }),
      };
      fetch.mockResolvedValue(mockResponse);

      const result = await apiService.request('/test');

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/test',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
        })
      );
      expect(result).toEqual({ success: true, data: 'test' });
    });

    test('應該拋出錯誤當 API 返回錯誤狀態', async () => {
      const mockResponse = {
        ok: false,
        status: 400,
        json: jest.fn().mockResolvedValue({ message: 'Bad Request' }),
      };
      fetch.mockResolvedValue(mockResponse);

      await expect(apiService.request('/test')).rejects.toThrow('Bad Request');
    });

    test('應該拋出錯誤當網路請求失敗', async () => {
      fetch.mockRejectedValue(new Error('Network Error'));

      await expect(apiService.request('/test')).rejects.toThrow('Network Error');
    });
  });

  describe('login', () => {
    test('應該發送登入請求並儲存 token', async () => {
      const mockResponse = {
        success: true,
        data: {
          token: 'test-token',
          user: { id: '1', username: 'test' },
        },
      };

      const mockFetchResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockResponse),
      };
      fetch.mockResolvedValue(mockFetchResponse);

      const credentials = { email: '<EMAIL>', password: 'password' };
      const result = await apiService.login(credentials);

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/auth/login',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(credentials),
        })
      );
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('auth_token', 'test-token');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('walletLogin', () => {
    test('應該發送錢包登入請求並儲存 token', async () => {
      const mockResponse = {
        success: true,
        data: {
          token: 'wallet-token',
          user: { id: '1', username: 'wallet_user' },
          walletAddress: 'test-wallet-address',
        },
      };

      const mockFetchResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockResponse),
      };
      fetch.mockResolvedValue(mockFetchResponse);

      const walletData = {
        walletAddress: 'test-wallet-address',
        signature: 'test-signature',
        message: 'test-message',
      };
      const result = await apiService.walletLogin(walletData);

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/auth/wallet-login',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(walletData),
        })
      );
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('auth_token', 'wallet-token');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('register', () => {
    test('應該發送註冊請求並儲存 token', async () => {
      const mockResponse = {
        success: true,
        data: {
          token: 'register-token',
          user: { id: '2', username: 'newuser' },
        },
      };

      const mockFetchResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockResponse),
      };
      fetch.mockResolvedValue(mockFetchResponse);

      const userData = {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
      };
      const result = await apiService.register(userData);

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/auth/register',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(userData),
        })
      );
      expect(mockAsyncStorage.setItem).toHaveBeenCalledWith('auth_token', 'register-token');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('logout', () => {
    test('應該移除 token 並發送登出請求', async () => {
      const mockResponse = {
        success: true,
        message: '登出成功',
      };

      const mockFetchResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue(mockResponse),
      };
      fetch.mockResolvedValue(mockFetchResponse);

      const result = await apiService.logout();

      expect(mockAsyncStorage.removeItem).toHaveBeenCalledWith('auth_token');
      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/auth/logout',
        expect.objectContaining({
          method: 'POST',
        })
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('HTTP methods', () => {
    test('get 方法應該發送 GET 請求', async () => {
      const mockResponse = { ok: true, json: jest.fn().mockResolvedValue({ data: 'test' }) };
      fetch.mockResolvedValue(mockResponse);

      await apiService.get('/test');

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/test',
        expect.objectContaining({
          method: 'GET',
        })
      );
    });

    test('post 方法應該發送 POST 請求', async () => {
      const mockResponse = { ok: true, json: jest.fn().mockResolvedValue({ data: 'test' }) };
      fetch.mockResolvedValue(mockResponse);

      const data = { test: 'data' };
      await apiService.post('/test', data);

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/test',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(data),
        })
      );
    });

    test('put 方法應該發送 PUT 請求', async () => {
      const mockResponse = { ok: true, json: jest.fn().mockResolvedValue({ data: 'test' }) };
      fetch.mockResolvedValue(mockResponse);

      const data = { test: 'data' };
      await apiService.put('/test', data);

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/test',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(data),
        })
      );
    });

    test('delete 方法應該發送 DELETE 請求', async () => {
      const mockResponse = { ok: true, json: jest.fn().mockResolvedValue({ data: 'test' }) };
      fetch.mockResolvedValue(mockResponse);

      await apiService.delete('/test');

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/test',
        expect.objectContaining({
          method: 'DELETE',
        })
      );
    });
  });
});
