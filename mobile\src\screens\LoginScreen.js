import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { loginWithWallet, loginWithCredentials } from '../store/slices/authSlice';
import { COLORS, SIZES } from '../utils/constants';

const LoginScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const { loading, error } = useSelector(state => state.auth);
  
  // 表單狀態
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isWalletConnecting, setIsWalletConnecting] = useState(false);

  // 錢包登入
  const handleWalletLogin = async () => {
    try {
      setIsWalletConnecting(true);
      const result = await dispatch(loginWithWallet());
      
      if (result.type === 'auth/loginWithWallet/fulfilled') {
        navigation.replace('Home');
      } else {
        Alert.alert('錢包登入失敗', result.payload || '未知錯誤');
      }
    } catch (error) {
      Alert.alert('錢包登入失敗', error.message);
    } finally {
      setIsWalletConnecting(false);
    }
  };

  // 傳統登入
  const handleCredentialsLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('輸入錯誤', '請輸入電子郵件和密碼');
      return;
    }

    try {
      const result = await dispatch(loginWithCredentials({ email, password }));
      
      if (result.type === 'auth/loginWithCredentials/fulfilled') {
        navigation.replace('Home');
      } else {
        Alert.alert('登入失敗', result.payload || '電子郵件或密碼錯誤');
      }
    } catch (error) {
      Alert.alert('登入失敗', error.message);
    }
  };

  // 導航到註冊頁面
  const navigateToRegister = () => {
    navigation.navigate('Register');
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView 
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        {/* Logo 區域 */}
        <View style={styles.logoContainer}>
          <View style={styles.logoPlaceholder}>
            <Text style={styles.logoText}>ChainXiang</Text>
            <Text style={styles.logoSubtext}>鏈象</Text>
          </View>
          <Text style={styles.tagline}>區塊鏈象棋對戰平台</Text>
        </View>

        {/* 登入選項 */}
        <View style={styles.loginContainer}>
          <Text style={styles.title}>歡迎回來</Text>
          
          {/* 錢包登入區域 */}
          <View style={styles.walletSection}>
            <Text style={styles.sectionTitle}>使用錢包登入</Text>
            <TouchableOpacity
              style={[
                styles.walletButton,
                (loading || isWalletConnecting) && styles.walletButtonDisabled,
              ]}
              onPress={handleWalletLogin}
              disabled={loading || isWalletConnecting}
            >
              {loading || isWalletConnecting ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator color={COLORS.TEXT_PRIMARY} size="small" />
                  <Text style={styles.walletButtonText}>連接中...</Text>
                </View>
              ) : (
                <>
                  <View style={styles.walletIcon}>
                    <Text style={styles.walletIconText}>👛</Text>
                  </View>
                  <Text style={styles.walletButtonText}>連接 Solana 錢包</Text>
                </>
              )}
            </TouchableOpacity>
          </View>

          {/* 分隔線 */}
          <View style={styles.divider}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>或</Text>
            <View style={styles.dividerLine} />
          </View>

          {/* 傳統登入區域 */}
          <View style={styles.credentialsSection}>
            <Text style={styles.sectionTitle}>使用帳號登入</Text>
            
            <TextInput
              style={styles.input}
              placeholder="電子郵件"
              placeholderTextColor={COLORS.TEXT_SECONDARY}
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
            />
            
            <TextInput
              style={styles.input}
              placeholder="密碼"
              placeholderTextColor={COLORS.TEXT_SECONDARY}
              value={password}
              onChangeText={setPassword}
              secureTextEntry
              autoCapitalize="none"
              autoCorrect={false}
            />
            
            <TouchableOpacity
              style={[
                styles.loginButton,
                loading && styles.loginButtonDisabled,
              ]}
              onPress={handleCredentialsLogin}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator color={COLORS.TEXT_PRIMARY} size="small" />
              ) : (
                <Text style={styles.loginButtonText}>登入</Text>
              )}
            </TouchableOpacity>
          </View>

          {/* 錯誤訊息 */}
          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}

          {/* 註冊連結 */}
          <View style={styles.registerSection}>
            <Text style={styles.registerText}>還沒有帳號？</Text>
            <TouchableOpacity onPress={navigateToRegister}>
              <Text style={styles.registerLink}>立即註冊</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 40,
    marginBottom: 40,
  },
  logoPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: COLORS.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
    elevation: 8,
    shadowColor: COLORS.PRIMARY,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  logoText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  logoSubtext: {
    fontSize: 14,
    color: COLORS.TEXT_PRIMARY,
    marginTop: 2,
  },
  tagline: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  loginContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: 32,
  },
  walletSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 12,
    textAlign: 'center',
  },
  walletButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: 24,
    paddingVertical: 14,
    borderRadius: SIZES.BORDER_RADIUS,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 4,
    shadowColor: COLORS.PRIMARY,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  walletButtonDisabled: {
    opacity: 0.6,
  },
  walletIcon: {
    marginRight: 8,
  },
  walletIconText: {
    fontSize: 20,
  },
  walletButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 24,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: COLORS.BORDER,
  },
  dividerText: {
    marginHorizontal: 16,
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
  },
  credentialsSection: {
    marginBottom: 24,
  },
  input: {
    backgroundColor: COLORS.SURFACE,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: SIZES.BORDER_RADIUS,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 16,
  },
  loginButton: {
    backgroundColor: COLORS.SECONDARY,
    paddingVertical: 14,
    borderRadius: SIZES.BORDER_RADIUS,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loginButtonDisabled: {
    opacity: 0.6,
  },
  loginButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  errorContainer: {
    backgroundColor: COLORS.ERROR + '20',
    borderWidth: 1,
    borderColor: COLORS.ERROR,
    borderRadius: SIZES.BORDER_RADIUS,
    padding: 12,
    marginBottom: 16,
  },
  errorText: {
    color: COLORS.ERROR,
    fontSize: 14,
    textAlign: 'center',
  },
  registerSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  registerText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginRight: 4,
  },
  registerLink: {
    fontSize: 14,
    color: COLORS.PRIMARY,
    fontWeight: '600',
  },
});

export default LoginScreen;
