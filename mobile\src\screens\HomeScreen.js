import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { logout } from '../store/slices/authSlice';
import { fetchUserProfile } from '../store/slices/userSlice';
import { setMatching } from '../store/slices/gameSlice';
import { COLORS, SIZES } from '../utils/constants';

const HomeScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const { user, walletAddress } = useSelector(state => state.auth);
  const { profile } = useSelector(state => state.user);
  const { isMatching } = useSelector(state => state.game);

  useEffect(() => {
    // 載入用戶資料
    dispatch(fetchUserProfile());
  }, [dispatch]);

  const handleStartGame = () => {
    if (isMatching) {
      // 取消匹配
      dispatch(setMatching(false));
      Alert.alert('已取消匹配', '您已退出匹配隊列');
    } else {
      // 開始匹配
      dispatch(setMatching(true));
      Alert.alert('開始匹配', '正在為您尋找對手...');
      
      // TODO: 實際的匹配邏輯
      // websocketService.requestMatch();
    }
  };

  const handleLogout = () => {
    Alert.alert(
      '確認登出',
      '您確定要登出嗎？',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '登出',
          style: 'destructive',
          onPress: () => {
            dispatch(logout());
            navigation.replace('Auth');
          },
        },
      ]
    );
  };

  const navigateToProfile = () => {
    navigation.navigate('Profile');
  };

  const navigateToHistory = () => {
    navigation.navigate('History');
  };

  const navigateToWallet = () => {
    navigation.navigate('Wallet');
  };

  return (
    <ScrollView style={styles.container}>
      {/* 用戶資訊卡片 */}
      <View style={styles.userCard}>
        <View style={styles.userInfo}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>
              {profile.username ? profile.username.charAt(0).toUpperCase() : '?'}
            </Text>
          </View>
          <View style={styles.userDetails}>
            <Text style={styles.username}>
              {profile.username || '未設置暱稱'}
            </Text>
            <Text style={styles.walletAddress}>
              {walletAddress ? `${walletAddress.slice(0, 8)}...${walletAddress.slice(-8)}` : ''}
            </Text>
            <View style={styles.statsRow}>
              <Text style={styles.level}>等級 {profile.level}</Text>
              <Text style={styles.rating}>積分 {profile.rating}</Text>
            </View>
          </View>
        </View>
        <TouchableOpacity style={styles.profileButton} onPress={navigateToProfile}>
          <Text style={styles.profileButtonText}>編輯</Text>
        </TouchableOpacity>
      </View>

      {/* 遊戲統計 */}
      <View style={styles.statsCard}>
        <Text style={styles.cardTitle}>遊戲統計</Text>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{profile.stats?.totalGames || 0}</Text>
            <Text style={styles.statLabel}>總場次</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{profile.stats?.wins || 0}</Text>
            <Text style={styles.statLabel}>勝場</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{profile.stats?.losses || 0}</Text>
            <Text style={styles.statLabel}>敗場</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{profile.stats?.draws || 0}</Text>
            <Text style={styles.statLabel}>和局</Text>
          </View>
        </View>
      </View>

      {/* 主要操作按鈕 */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[
            styles.playButton,
            isMatching && styles.playButtonMatching,
          ]}
          onPress={handleStartGame}
        >
          <Text style={styles.playButtonText}>
            {isMatching ? '取消匹配' : '開始對戰'}
          </Text>
          {isMatching && (
            <Text style={styles.matchingText}>正在尋找對手...</Text>
          )}
        </TouchableOpacity>

        <View style={styles.secondaryButtons}>
          <TouchableOpacity style={styles.secondaryButton} onPress={navigateToHistory}>
            <Text style={styles.secondaryButtonText}>歷史記錄</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryButton} onPress={navigateToWallet}>
            <Text style={styles.secondaryButtonText}>錢包管理</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.secondaryButton}>
            <Text style={styles.secondaryButtonText}>排行榜</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* 快速資訊 */}
      <View style={styles.infoCard}>
        <Text style={styles.cardTitle}>遊戲資訊</Text>
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>網路狀態</Text>
          <Text style={styles.infoValue}>已連接</Text>
        </View>
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>在線玩家</Text>
          <Text style={styles.infoValue}>1,234</Text>
        </View>
        <View style={styles.infoItem}>
          <Text style={styles.infoLabel}>進行中遊戲</Text>
          <Text style={styles.infoValue}>567</Text>
        </View>
      </View>

      {/* 登出按鈕 */}
      <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
        <Text style={styles.logoutButtonText}>登出</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
    padding: 20,
  },
  userCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS,
    padding: 20,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 3,
    shadowColor: COLORS.PRIMARY,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: COLORS.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  avatarText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  userDetails: {
    flex: 1,
  },
  username: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 4,
  },
  walletAddress: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 8,
  },
  statsRow: {
    flexDirection: 'row',
  },
  level: {
    fontSize: 14,
    color: COLORS.ACCENT,
    marginRight: 16,
  },
  rating: {
    fontSize: 14,
    color: COLORS.SUCCESS,
  },
  profileButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: SIZES.BORDER_RADIUS,
  },
  profileButtonText: {
    color: COLORS.TEXT_PRIMARY,
    fontSize: 14,
    fontWeight: 'bold',
  },
  statsCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS,
    padding: 20,
    marginBottom: 20,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
  },
  actionButtons: {
    marginBottom: 20,
  },
  playButton: {
    backgroundColor: COLORS.SUCCESS,
    paddingVertical: 20,
    borderRadius: SIZES.BORDER_RADIUS,
    alignItems: 'center',
    marginBottom: 16,
    elevation: 4,
    shadowColor: COLORS.SUCCESS,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  playButtonMatching: {
    backgroundColor: COLORS.WARNING,
  },
  playButtonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  matchingText: {
    fontSize: 14,
    color: COLORS.TEXT_PRIMARY,
    marginTop: 4,
    opacity: 0.8,
  },
  secondaryButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  secondaryButton: {
    backgroundColor: COLORS.SURFACE,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: SIZES.BORDER_RADIUS,
    flex: 0.48,
    alignItems: 'center',
    elevation: 2,
  },
  secondaryButtonText: {
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '600',
  },
  infoCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS,
    padding: 20,
    marginBottom: 20,
    elevation: 3,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  infoLabel: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
  },
  infoValue: {
    fontSize: 14,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '600',
  },
  logoutButton: {
    backgroundColor: COLORS.ERROR,
    paddingVertical: 16,
    borderRadius: SIZES.BORDER_RADIUS,
    alignItems: 'center',
    marginBottom: 40,
  },
  logoutButtonText: {
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: 'bold',
  },
});

export default HomeScreen;
