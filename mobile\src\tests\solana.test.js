// Solana 服務測試
import { solanaService } from '../services/solana';

describe('Solana Service', () => {
  test('應該能夠初始化 Solana 服務', () => {
    expect(solanaService).toBeDefined();
    expect(solanaService.getNetworkInfo).toBeDefined();
  });

  test('應該能夠獲取網路資訊', () => {
    const networkInfo = solanaService.getNetworkInfo();
    expect(networkInfo).toHaveProperty('network');
    expect(networkInfo).toHaveProperty('endpoint');
    expect(networkInfo.network).toBe('devnet');
  });

  test('應該能夠模擬錢包連接', async () => {
    const publicKey = await solanaService.connectWallet();
    expect(publicKey).toBeDefined();
    expect(typeof publicKey).toBe('string');
    expect(solanaService.isConnected()).toBe(true);
  });

  test('應該能夠獲取模擬餘額', async () => {
    await solanaService.connectWallet();
    const balance = await solanaService.getBalance();
    expect(balance).toBeDefined();
    expect(typeof balance).toBe('number');
    expect(balance).toBeGreaterThanOrEqual(0);
  });

  test('應該能夠簽名訊息', async () => {
    await solanaService.connectWallet();
    const message = 'test message';
    const signature = await solanaService.signMessage(message);
    expect(signature).toBeDefined();
    expect(Array.isArray(signature)).toBe(true);
  });

  test('應該能夠獲取交易歷史', async () => {
    await solanaService.connectWallet();
    const history = await solanaService.getTransactionHistory();
    expect(history).toBeDefined();
    expect(Array.isArray(history)).toBe(true);
  });

  test('應該能夠斷開錢包連接', async () => {
    await solanaService.connectWallet();
    expect(solanaService.isConnected()).toBe(true);
    
    await solanaService.disconnectWallet();
    expect(solanaService.isConnected()).toBe(false);
  });
});
