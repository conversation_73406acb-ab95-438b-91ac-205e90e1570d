import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { solanaService } from '../../services/solana';
import { apiService } from '../../services/api';

// 異步錢包登入操作
export const loginWithWallet = createAsyncThunk(
  'auth/loginWithWallet',
  async (_, { rejectWithValue }) => {
    try {
      // 1. 連接錢包
      const walletAddress = await solanaService.connectWallet();

      // 2. 簽名訊息
      const message = `ChainXiang Login: ${Date.now()}`;
      const signature = await solanaService.signMessage(message);

      // 3. 使用錢包登入 API
      const response = await apiService.walletLogin({
        walletAddress,
        signature,
        message,
      });

      return {
        token: response.data.token,
        user: response.data.user,
        walletAddress: response.data.walletAddress,
      };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// 傳統登入操作
export const loginWithCredentials = createAsyncThunk(
  'auth/loginWithCredentials',
  async ({ email, password }, { rejectWithValue }) => {
    try {
      const response = await apiService.login({ email, password });

      return {
        token: response.data.token,
        user: response.data.user,
      };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// 註冊操作
export const registerUser = createAsyncThunk(
  'auth/registerUser',
  async ({ username, email, password }, { rejectWithValue }) => {
    try {
      const response = await apiService.register({ username, email, password });

      return {
        token: response.data.token,
        user: response.data.user,
      };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// 連接錢包操作（已登入用戶）
export const connectWallet = createAsyncThunk(
  'auth/connectWallet',
  async (_, { rejectWithValue }) => {
    try {
      // 1. 連接錢包
      const walletAddress = await solanaService.connectWallet();

      // 2. 簽名訊息
      const message = `ChainXiang Wallet Connect: ${Date.now()}`;
      const signature = await solanaService.signMessage(message);

      // 3. 後端驗證並連接
      const response = await apiService.post('/auth/wallet-connect', {
        walletAddress,
        signature,
        message,
      });

      return {
        user: response.data.user,
        walletAddress: response.data.walletAddress,
      };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

// 斷開錢包連接
export const disconnectWallet = createAsyncThunk(
  'auth/disconnectWallet',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.post('/auth/wallet-disconnect');

      return {
        user: response.data.user,
      };
    } catch (error) {
      return rejectWithValue(error.message);
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState: {
    isAuthenticated: false,
    token: null,
    walletAddress: null,
    user: null,
    loading: false,
    error: null,
  },
  reducers: {
    logout: (state) => {
      state.isAuthenticated = false;
      state.token = null;
      state.walletAddress = null;
      state.user = null;
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // 錢包登入
      .addCase(loginWithWallet.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginWithWallet.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.walletAddress = action.payload.walletAddress;
        state.user = action.payload.user;
        state.error = null;
      })
      .addCase(loginWithWallet.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 傳統登入
      .addCase(loginWithCredentials.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginWithCredentials.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.user = action.payload.user;
        state.walletAddress = null; // 傳統登入沒有錢包地址
        state.error = null;
      })
      .addCase(loginWithCredentials.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 註冊
      .addCase(registerUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.user = action.payload.user;
        state.walletAddress = null; // 註冊時沒有錢包地址
        state.error = null;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 連接錢包
      .addCase(connectWallet.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(connectWallet.fulfilled, (state, action) => {
        state.loading = false;
        state.walletAddress = action.payload.walletAddress;
        state.user = action.payload.user;
        state.error = null;
      })
      .addCase(connectWallet.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // 斷開錢包連接
      .addCase(disconnectWallet.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(disconnectWallet.fulfilled, (state, action) => {
        state.loading = false;
        state.walletAddress = null;
        state.user = action.payload.user;
        state.error = null;
      })
      .addCase(disconnectWallet.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

export const { logout, clearError } = authSlice.actions;
export { loginWithCredentials, registerUser, connectWallet, disconnectWallet };
export default authSlice.reducer;
