// 錢包登入 API 測試腳本
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testWalletLogin() {
  try {
    console.log('測試錢包登入...');
    
    // 模擬錢包數據
    const mockWalletData = {
      walletAddress: '9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM',
      signature: 'mock_signature_' + Date.now(),
      message: `ChainXiang Login: ${Date.now()}`
    };
    
    const response = await axios.post(`${BASE_URL}/auth/wallet-login`, mockWalletData);
    
    console.log('錢包登入成功:', response.data);
    return response.data.data.token;
  } catch (error) {
    console.error('錢包登入失敗:', error.response?.data || error.message);
    return null;
  }
}

async function testWalletLoginWithExistingWallet() {
  try {
    console.log('\n測試已存在錢包的登入...');
    
    // 使用相同的錢包地址再次登入
    const mockWalletData = {
      walletAddress: '9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM',
      signature: 'mock_signature_' + Date.now(),
      message: `ChainXiang Login: ${Date.now()}`
    };
    
    const response = await axios.post(`${BASE_URL}/auth/wallet-login`, mockWalletData);
    
    console.log('已存在錢包登入成功:', response.data);
    return response.data.data.token;
  } catch (error) {
    console.error('已存在錢包登入失敗:', error.response?.data || error.message);
    return null;
  }
}

async function testGetCurrentUserWithWalletToken(token) {
  try {
    console.log('\n測試使用錢包 token 獲取用戶資訊...');
    const response = await axios.get(`${BASE_URL}/auth/me`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    console.log('獲取錢包用戶資訊成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('獲取錢包用戶資訊失敗:', error.response?.data || error.message);
    return null;
  }
}

async function testInvalidWalletLogin() {
  try {
    console.log('\n測試無效錢包登入...');
    
    // 測試缺少必要欄位
    const invalidData = {
      walletAddress: '9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM'
      // 缺少 signature 和 message
    };
    
    const response = await axios.post(`${BASE_URL}/auth/wallet-login`, invalidData);
    console.log('意外成功:', response.data);
  } catch (error) {
    console.log('預期的錯誤:', error.response?.data || error.message);
  }
}

async function testInvalidWalletAddress() {
  try {
    console.log('\n測試無效錢包地址...');
    
    const invalidData = {
      walletAddress: 'invalid_wallet_address',
      signature: 'mock_signature',
      message: 'test message'
    };
    
    const response = await axios.post(`${BASE_URL}/auth/wallet-login`, invalidData);
    console.log('意外成功:', response.data);
  } catch (error) {
    console.log('預期的錯誤:', error.response?.data || error.message);
  }
}

async function runWalletTests() {
  console.log('開始錢包 API 測試...\n');
  
  // 測試首次錢包登入（創建新用戶）
  const firstToken = await testWalletLogin();
  
  // 測試已存在錢包的登入
  const secondToken = await testWalletLoginWithExistingWallet();
  
  // 測試使用錢包 token 獲取用戶資訊
  if (firstToken) {
    await testGetCurrentUserWithWalletToken(firstToken);
  }
  
  // 測試錯誤情況
  await testInvalidWalletLogin();
  await testInvalidWalletAddress();
  
  console.log('\n錢包 API 測試完成！');
}

// 運行測試
runWalletTests().catch(console.error);
