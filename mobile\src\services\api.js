import AsyncStorage from '@react-native-async-storage/async-storage';

const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api' 
  : 'https://api.chainxiang.com/api';

class ApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // 獲取認證標頭
  async getAuthHeaders() {
    const token = await AsyncStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  // 通用請求方法
  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const headers = await this.getAuthHeaders();
    
    const config = {
      headers,
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API Request Error:', error);
      throw error;
    }
  }

  // GET 請求
  async get(endpoint) {
    return this.request(endpoint, { method: 'GET' });
  }

  // POST 請求
  async post(endpoint, data) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // PUT 請求
  async put(endpoint, data) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // DELETE 請求
  async delete(endpoint) {
    return this.request(endpoint, { method: 'DELETE' });
  }

  // 認證相關 API
  async login(credentials) {
    const response = await this.post('/auth/login', credentials);

    // 儲存 token
    if (response.data.token) {
      await AsyncStorage.setItem('auth_token', response.data.token);
    }

    return response;
  }

  // 錢包登入
  async walletLogin(walletData) {
    const response = await this.post('/auth/wallet-login', walletData);

    // 儲存 token
    if (response.data.token) {
      await AsyncStorage.setItem('auth_token', response.data.token);
    }

    return response;
  }

  // 註冊
  async register(userData) {
    const response = await this.post('/auth/register', userData);

    // 儲存 token
    if (response.data.token) {
      await AsyncStorage.setItem('auth_token', response.data.token);
    }

    return response;
  }

  async logout() {
    await AsyncStorage.removeItem('auth_token');
    return this.post('/auth/logout');
  }

  async refreshToken() {
    return this.post('/auth/refresh');
  }

  // 用戶相關 API
  async getUserProfile() {
    return this.get('/user/profile');
  }

  async updateUserProfile(profileData) {
    return this.put('/user/profile', profileData);
  }

  async getUserStats() {
    return this.get('/user/stats');
  }

  // 遊戲相關 API
  async getGameHistory(page = 1, limit = 10) {
    return this.get(`/games/history?page=${page}&limit=${limit}`);
  }

  async getGameDetails(gameId) {
    return this.get(`/games/${gameId}`);
  }

  async getGameReplay(gameId) {
    return this.get(`/games/${gameId}/replay`);
  }

  // 匹配相關 API
  async joinMatchQueue() {
    return this.post('/match/queue');
  }

  async leaveMatchQueue() {
    return this.delete('/match/queue');
  }

  async getMatchStatus() {
    return this.get('/match/status');
  }

  // 排行榜 API
  async getLeaderboard(page = 1, limit = 50) {
    return this.get(`/leaderboard?page=${page}&limit=${limit}`);
  }

  // 系統 API
  async getSystemStatus() {
    return this.get('/system/status');
  }

  async reportBug(bugReport) {
    return this.post('/system/bug-report', bugReport);
  }
}

export const apiService = new ApiService();
