import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { registerUser } from '../store/slices/authSlice';
import { COLORS, SIZES } from '../utils/constants';

const RegisterScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const { loading, error } = useSelector(state => state.auth);
  
  // 表單狀態
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // 表單驗證
  const validateForm = () => {
    if (!username.trim()) {
      Alert.alert('輸入錯誤', '請輸入用戶名');
      return false;
    }
    
    if (username.length < 3) {
      Alert.alert('輸入錯誤', '用戶名至少需要3個字符');
      return false;
    }
    
    if (!email.trim()) {
      Alert.alert('輸入錯誤', '請輸入電子郵件');
      return false;
    }
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert('輸入錯誤', '請輸入有效的電子郵件地址');
      return false;
    }
    
    if (!password.trim()) {
      Alert.alert('輸入錯誤', '請輸入密碼');
      return false;
    }
    
    if (password.length < 6) {
      Alert.alert('輸入錯誤', '密碼至少需要6個字符');
      return false;
    }
    
    if (password !== confirmPassword) {
      Alert.alert('輸入錯誤', '密碼確認不匹配');
      return false;
    }
    
    return true;
  };

  // 處理註冊
  const handleRegister = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const result = await dispatch(registerUser({ 
        username: username.trim(), 
        email: email.trim(), 
        password 
      }));
      
      if (result.type === 'auth/registerUser/fulfilled') {
        Alert.alert(
          '註冊成功',
          '歡迎加入 ChainXiang！',
          [
            {
              text: '確定',
              onPress: () => navigation.replace('Home'),
            },
          ]
        );
      } else {
        Alert.alert('註冊失敗', result.payload || '註冊過程中發生錯誤');
      }
    } catch (error) {
      Alert.alert('註冊失敗', error.message);
    }
  };

  // 導航到登入頁面
  const navigateToLogin = () => {
    navigation.navigate('Login');
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView 
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
      >
        {/* Logo 區域 */}
        <View style={styles.logoContainer}>
          <View style={styles.logoPlaceholder}>
            <Text style={styles.logoText}>ChainXiang</Text>
            <Text style={styles.logoSubtext}>鏈象</Text>
          </View>
          <Text style={styles.tagline}>區塊鏈象棋對戰平台</Text>
        </View>

        {/* 註冊表單 */}
        <View style={styles.formContainer}>
          <Text style={styles.title}>創建新帳號</Text>
          
          <TextInput
            style={styles.input}
            placeholder="用戶名"
            placeholderTextColor={COLORS.TEXT_SECONDARY}
            value={username}
            onChangeText={setUsername}
            autoCapitalize="none"
            autoCorrect={false}
          />
          
          <TextInput
            style={styles.input}
            placeholder="電子郵件"
            placeholderTextColor={COLORS.TEXT_SECONDARY}
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            autoCorrect={false}
          />
          
          <TextInput
            style={styles.input}
            placeholder="密碼"
            placeholderTextColor={COLORS.TEXT_SECONDARY}
            value={password}
            onChangeText={setPassword}
            secureTextEntry
            autoCapitalize="none"
            autoCorrect={false}
          />
          
          <TextInput
            style={styles.input}
            placeholder="確認密碼"
            placeholderTextColor={COLORS.TEXT_SECONDARY}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            secureTextEntry
            autoCapitalize="none"
            autoCorrect={false}
          />
          
          <TouchableOpacity
            style={[
              styles.registerButton,
              loading && styles.registerButtonDisabled,
            ]}
            onPress={handleRegister}
            disabled={loading}
          >
            {loading ? (
              <ActivityIndicator color={COLORS.TEXT_PRIMARY} size="small" />
            ) : (
              <Text style={styles.registerButtonText}>註冊</Text>
            )}
          </TouchableOpacity>

          {/* 錯誤訊息 */}
          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>{error}</Text>
            </View>
          )}

          {/* 登入連結 */}
          <View style={styles.loginSection}>
            <Text style={styles.loginText}>已經有帳號？</Text>
            <TouchableOpacity onPress={navigateToLogin}>
              <Text style={styles.loginLink}>立即登入</Text>
            </TouchableOpacity>
          </View>

          {/* 服務條款 */}
          <View style={styles.termsContainer}>
            <Text style={styles.termsText}>
              註冊即表示您同意我們的
              <Text style={styles.termsLink}> 服務條款 </Text>
              和
              <Text style={styles.termsLink}> 隱私政策</Text>
            </Text>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingVertical: 40,
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 40,
  },
  logoPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: COLORS.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    elevation: 8,
    shadowColor: COLORS.PRIMARY,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  logoText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  logoSubtext: {
    fontSize: 12,
    color: COLORS.TEXT_PRIMARY,
    marginTop: 2,
  },
  tagline: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  formContainer: {
    flex: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: 32,
  },
  input: {
    backgroundColor: COLORS.SURFACE,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: SIZES.BORDER_RADIUS,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 16,
  },
  registerButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: 14,
    borderRadius: SIZES.BORDER_RADIUS,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 8,
    elevation: 4,
    shadowColor: COLORS.PRIMARY,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  registerButtonDisabled: {
    opacity: 0.6,
  },
  registerButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  errorContainer: {
    backgroundColor: COLORS.ERROR + '20',
    borderWidth: 1,
    borderColor: COLORS.ERROR,
    borderRadius: SIZES.BORDER_RADIUS,
    padding: 12,
    marginTop: 16,
  },
  errorText: {
    color: COLORS.ERROR,
    fontSize: 14,
    textAlign: 'center',
  },
  loginSection: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
  },
  loginText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginRight: 4,
  },
  loginLink: {
    fontSize: 14,
    color: COLORS.PRIMARY,
    fontWeight: '600',
  },
  termsContainer: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  termsText: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    lineHeight: 18,
  },
  termsLink: {
    color: COLORS.PRIMARY,
    fontWeight: '500',
  },
});

export default RegisterScreen;
