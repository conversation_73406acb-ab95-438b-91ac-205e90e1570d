import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  ScrollView,
  RefreshControl,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { solanaService } from '../services/solana';
import { apiService } from '../services/api';
import { COLORS, SIZES } from '../utils/constants';

const WalletScreen = ({ navigation }) => {
  const dispatch = useDispatch();
  const { user, walletAddress, isAuthenticated } = useSelector(state => state.auth);
  
  // 錢包狀態
  const [walletInfo, setWalletInfo] = useState({
    balance: 0,
    transactions: [],
    networkStatus: null,
  });
  
  // 載入狀態
  const [loading, setLoading] = useState({
    balance: false,
    transactions: false,
    connecting: false,
    disconnecting: false,
  });
  
  const [refreshing, setRefreshing] = useState(false);

  // 載入錢包資訊
  const loadWalletInfo = async () => {
    if (!walletAddress) return;

    try {
      setLoading(prev => ({ ...prev, balance: true, transactions: true }));

      // 獲取餘額
      const balance = await solanaService.getBalance(walletAddress);
      
      // 獲取交易歷史
      const transactions = await solanaService.getTransactionHistory(walletAddress);
      
      // 獲取網路狀態
      const networkStatus = await solanaService.getNetworkInfo();

      setWalletInfo({
        balance,
        transactions: transactions.slice(0, 10), // 只顯示最近10筆
        networkStatus,
      });
    } catch (error) {
      console.error('載入錢包資訊失敗:', error);
      Alert.alert('錯誤', '載入錢包資訊失敗');
    } finally {
      setLoading(prev => ({ ...prev, balance: false, transactions: false }));
    }
  };

  // 連接新錢包
  const handleConnectWallet = async () => {
    try {
      setLoading(prev => ({ ...prev, connecting: true }));

      // 1. 連接錢包
      const newWalletAddress = await solanaService.connectWallet();
      
      // 2. 簽名訊息
      const message = `ChainXiang Wallet Connect: ${Date.now()}`;
      const signature = await solanaService.signMessage(message);
      
      // 3. 後端驗證並連接
      const response = await apiService.post('/auth/wallet-connect', {
        walletAddress: newWalletAddress,
        signature,
        message,
      });

      Alert.alert('成功', '錢包連接成功！', [
        {
          text: '確定',
          onPress: () => {
            // 重新載入用戶資訊
            loadWalletInfo();
          },
        },
      ]);
    } catch (error) {
      console.error('錢包連接失敗:', error);
      Alert.alert('錯誤', `錢包連接失敗: ${error.message}`);
    } finally {
      setLoading(prev => ({ ...prev, connecting: false }));
    }
  };

  // 斷開錢包連接
  const handleDisconnectWallet = () => {
    Alert.alert(
      '確認斷開',
      '您確定要斷開錢包連接嗎？這將影響您的遊戲體驗。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '確定',
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(prev => ({ ...prev, disconnecting: true }));
              
              await apiService.post('/auth/wallet-disconnect');
              
              Alert.alert('成功', '錢包連接已斷開');
              
              // 清除本地錢包資訊
              setWalletInfo({
                balance: 0,
                transactions: [],
                networkStatus: null,
              });
            } catch (error) {
              console.error('斷開錢包失敗:', error);
              Alert.alert('錯誤', `斷開錢包失敗: ${error.message}`);
            } finally {
              setLoading(prev => ({ ...prev, disconnecting: false }));
            }
          },
        },
      ]
    );
  };

  // 下拉刷新
  const onRefresh = async () => {
    setRefreshing(true);
    await loadWalletInfo();
    setRefreshing(false);
  };

  // 格式化餘額顯示
  const formatBalance = (balance) => {
    return (balance / 1000000000).toFixed(4); // SOL 有 9 位小數
  };

  // 格式化地址顯示
  const formatAddress = (address) => {
    if (!address) return '';
    return `${address.slice(0, 8)}...${address.slice(-8)}`;
  };

  // 初始載入
  useEffect(() => {
    if (walletAddress) {
      loadWalletInfo();
    }
  }, [walletAddress]);

  if (!isAuthenticated) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyState}>
          <Text style={styles.emptyTitle}>請先登入</Text>
          <Text style={styles.emptyText}>您需要先登入才能查看錢包資訊</Text>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={() => navigation.navigate('Auth')}
          >
            <Text style={styles.loginButtonText}>前往登入</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* 錢包狀態卡片 */}
      <View style={styles.walletCard}>
        <View style={styles.walletHeader}>
          <Text style={styles.walletTitle}>Solana 錢包</Text>
          {walletInfo.networkStatus && (
            <View style={styles.networkBadge}>
              <Text style={styles.networkText}>
                {walletInfo.networkStatus.network}
              </Text>
            </View>
          )}
        </View>

        {walletAddress ? (
          <>
            {/* 錢包地址 */}
            <View style={styles.addressSection}>
              <Text style={styles.addressLabel}>錢包地址</Text>
              <Text style={styles.addressText}>{formatAddress(walletAddress)}</Text>
            </View>

            {/* 餘額 */}
            <View style={styles.balanceSection}>
              <Text style={styles.balanceLabel}>餘額</Text>
              {loading.balance ? (
                <ActivityIndicator color={COLORS.PRIMARY} />
              ) : (
                <Text style={styles.balanceText}>
                  {formatBalance(walletInfo.balance)} SOL
                </Text>
              )}
            </View>

            {/* 操作按鈕 */}
            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={styles.refreshButton}
                onPress={loadWalletInfo}
                disabled={loading.balance || loading.transactions}
              >
                <Text style={styles.refreshButtonText}>刷新</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.disconnectButton, loading.disconnecting && styles.buttonDisabled]}
                onPress={handleDisconnectWallet}
                disabled={loading.disconnecting}
              >
                {loading.disconnecting ? (
                  <ActivityIndicator color={COLORS.TEXT_PRIMARY} size="small" />
                ) : (
                  <Text style={styles.disconnectButtonText}>斷開連接</Text>
                )}
              </TouchableOpacity>
            </View>
          </>
        ) : (
          <>
            {/* 未連接狀態 */}
            <View style={styles.noWalletSection}>
              <Text style={styles.noWalletText}>尚未連接錢包</Text>
              <Text style={styles.noWalletSubtext}>
                連接 Solana 錢包以享受完整的遊戲體驗
              </Text>
            </View>

            <TouchableOpacity
              style={[styles.connectButton, loading.connecting && styles.buttonDisabled]}
              onPress={handleConnectWallet}
              disabled={loading.connecting}
            >
              {loading.connecting ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator color={COLORS.TEXT_PRIMARY} size="small" />
                  <Text style={styles.connectButtonText}>連接中...</Text>
                </View>
              ) : (
                <Text style={styles.connectButtonText}>連接錢包</Text>
              )}
            </TouchableOpacity>
          </>
        )}
      </View>

      {/* 交易歷史 */}
      {walletAddress && (
        <View style={styles.transactionCard}>
          <Text style={styles.transactionTitle}>最近交易</Text>
          
          {loading.transactions ? (
            <View style={styles.loadingSection}>
              <ActivityIndicator color={COLORS.PRIMARY} />
              <Text style={styles.loadingText}>載入中...</Text>
            </View>
          ) : walletInfo.transactions.length > 0 ? (
            walletInfo.transactions.map((tx, index) => (
              <View key={index} style={styles.transactionItem}>
                <View style={styles.transactionInfo}>
                  <Text style={styles.transactionHash}>
                    {formatAddress(tx.signature)}
                  </Text>
                  <Text style={styles.transactionTime}>
                    {new Date(tx.blockTime * 1000).toLocaleDateString()}
                  </Text>
                </View>
                <Text style={styles.transactionAmount}>
                  {tx.amount > 0 ? '+' : ''}{formatBalance(tx.amount)} SOL
                </Text>
              </View>
            ))
          ) : (
            <Text style={styles.noTransactionText}>暫無交易記錄</Text>
          )}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 12,
  },
  emptyText: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: 24,
  },
  loginButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: SIZES.BORDER_RADIUS,
  },
  loginButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  walletCard: {
    backgroundColor: COLORS.SURFACE,
    margin: 16,
    padding: 20,
    borderRadius: SIZES.BORDER_RADIUS,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  walletHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  walletTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  networkBadge: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  networkText: {
    fontSize: 12,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  addressSection: {
    marginBottom: 16,
  },
  addressLabel: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 4,
  },
  addressText: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
  },
  balanceSection: {
    marginBottom: 20,
  },
  balanceLabel: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: 4,
  },
  balanceText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.PRIMARY,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  refreshButton: {
    flex: 1,
    backgroundColor: COLORS.SECONDARY,
    paddingVertical: 12,
    borderRadius: SIZES.BORDER_RADIUS,
    alignItems: 'center',
    marginRight: 8,
  },
  refreshButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  disconnectButton: {
    flex: 1,
    backgroundColor: COLORS.ERROR,
    paddingVertical: 12,
    borderRadius: SIZES.BORDER_RADIUS,
    alignItems: 'center',
    marginLeft: 8,
  },
  disconnectButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  noWalletSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  noWalletText: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 8,
  },
  noWalletSubtext: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  connectButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: 14,
    borderRadius: SIZES.BORDER_RADIUS,
    alignItems: 'center',
  },
  connectButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  transactionCard: {
    backgroundColor: COLORS.SURFACE,
    margin: 16,
    marginTop: 0,
    padding: 20,
    borderRadius: SIZES.BORDER_RADIUS,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  transactionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 16,
  },
  loadingSection: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  loadingText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginTop: 8,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionHash: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.TEXT_PRIMARY,
  },
  transactionTime: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    marginTop: 2,
  },
  transactionAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.PRIMARY,
  },
  noTransactionText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    paddingVertical: 20,
  },
});

export default WalletScreen;
