const { Connection, PublicKey, LAMPORTS_PER_SOL } = require('@solana/web3.js');
const logger = require('../utils/logger');

class SolanaService {
  constructor() {
    // Solana 網路配置
    this.network = process.env.SOLANA_NETWORK || 'devnet';
    this.rpcUrl = process.env.SOLANA_RPC_URL || 'https://api.devnet.solana.com';

    // 初始化連接
    this.connection = new Connection(this.rpcUrl, 'confirmed');

    logger.info(`Solana 服務初始化完成，網路: ${this.network}`);
  }

  // 驗證公鑰格式
  isValidPublicKey(publicKeyString) {
    try {
      new PublicKey(publicKeyString);
      return true;
    } catch (error) {
      return false;
    }
  }

  // 獲取帳戶餘額
  async getBalance(publicKeyString) {
    try {
      if (!this.isValidPublicKey(publicKeyString)) {
        throw new Error('無效的公鑰格式');
      }

      const publicKey = new PublicKey(publicKeyString);
      const balance = await this.connection.getBalance(publicKey);

      // 轉換為 SOL
      const balanceInSol = balance / LAMPORTS_PER_SOL;

      logger.info(`獲取餘額成功: ${publicKeyString} = ${balanceInSol} SOL`);
      return balanceInSol;
    } catch (error) {
      logger.error('獲取餘額失敗:', error);
      throw error;
    }
  }

  // 獲取帳戶資訊
  async getAccountInfo(publicKeyString) {
    try {
      if (!this.isValidPublicKey(publicKeyString)) {
        throw new Error('無效的公鑰格式');
      }

      const publicKey = new PublicKey(publicKeyString);
      const accountInfo = await this.connection.getAccountInfo(publicKey);

      if (!accountInfo) {
        return null;
      }

      return {
        lamports: accountInfo.lamports,
        owner: accountInfo.owner.toString(),
        executable: accountInfo.executable,
        rentEpoch: accountInfo.rentEpoch,
      };
    } catch (error) {
      logger.error('獲取帳戶資訊失敗:', error);
      throw error;
    }
  }

  // 獲取交易歷史
  async getTransactionHistory(publicKeyString, limit = 10) {
    try {
      if (!this.isValidPublicKey(publicKeyString)) {
        throw new Error('無效的公鑰格式');
      }

      const publicKey = new PublicKey(publicKeyString);
      const signatures = await this.connection.getSignaturesForAddress(
        publicKey,
        { limit },
      );

      const transactions = signatures.map(sig => ({
        signature: sig.signature,
        slot: sig.slot,
        timestamp: sig.blockTime,
        confirmationStatus: sig.confirmationStatus,
        err: sig.err,
      }));

      logger.info(`獲取交易歷史成功: ${publicKeyString}, 數量: ${transactions.length}`);
      return transactions;
    } catch (error) {
      logger.error('獲取交易歷史失敗:', error);
      throw error;
    }
  }

  // 驗證簽名
  async verifySignature(message, signature, publicKeyString) {
    try {
      if (!this.isValidPublicKey(publicKeyString)) {
        throw new Error('無效的公鑰格式');
      }

      // TODO: 實現簽名驗證邏輯
      // 這裡需要使用 nacl 或其他加密庫來驗證簽名

      logger.info(`簽名驗證請求: ${publicKeyString}`);

      // 暫時返回 true，實際實現需要真正的簽名驗證
      return true;
    } catch (error) {
      logger.error('簽名驗證失敗:', error);
      throw error;
    }
  }

  // 獲取網路狀態
  async getNetworkStatus() {
    try {
      const slot = await this.connection.getSlot();
      const blockTime = await this.connection.getBlockTime(slot);
      const epochInfo = await this.connection.getEpochInfo();

      return {
        network: this.network,
        rpcUrl: this.rpcUrl,
        currentSlot: slot,
        blockTime,
        epoch: epochInfo.epoch,
        slotIndex: epochInfo.slotIndex,
        slotsInEpoch: epochInfo.slotsInEpoch,
      };
    } catch (error) {
      logger.error('獲取網路狀態失敗:', error);
      throw error;
    }
  }

  // 檢查連接狀態
  async checkConnection() {
    try {
      const version = await this.connection.getVersion();
      logger.info('Solana 連接正常:', version);
      return true;
    } catch (error) {
      logger.error('Solana 連接檢查失敗:', error);
      return false;
    }
  }
}

// 創建單例實例
const solanaService = new SolanaService();

module.exports = {
  solanaService,
  SolanaService,
};
