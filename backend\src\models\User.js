const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
  // 基本資訊
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 20,
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
  },
  password: {
    type: String,
    required: true,
    minlength: 6,
  },

  // Solana 錢包資訊
  walletAddress: {
    type: String,
    unique: true,
    sparse: true, // 允許多個 null 值
  },
  walletConnected: {
    type: Boolean,
    default: false,
  },

  // 遊戲統計
  gameStats: {
    totalGames: {
      type: Number,
      default: 0,
    },
    wins: {
      type: Number,
      default: 0,
    },
    losses: {
      type: Number,
      default: 0,
    },
    draws: {
      type: Number,
      default: 0,
    },
    rating: {
      type: Number,
      default: 1200, // ELO 評分系統起始分數
    },
  },

  // 個人資料
  profile: {
    avatar: {
      type: String,
      default: null,
    },
    bio: {
      type: String,
      maxlength: 200,
      default: '',
    },
    country: {
      type: String,
      default: '',
    },
    preferredLanguage: {
      type: String,
      default: 'zh-TW',
    },
  },

  // 帳戶狀態
  isActive: {
    type: Boolean,
    default: true,
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  lastLogin: {
    type: Date,
    default: null,
  },

  // 設定
  settings: {
    notifications: {
      email: {
        type: Boolean,
        default: true,
      },
      push: {
        type: Boolean,
        default: true,
      },
    },
    privacy: {
      showOnline: {
        type: Boolean,
        default: true,
      },
      showStats: {
        type: Boolean,
        default: true,
      },
    },
  },
}, {
  timestamps: true, // 自動添加 createdAt 和 updatedAt
});

// 索引
userSchema.index({ username: 1 });
userSchema.index({ email: 1 });
userSchema.index({ walletAddress: 1 });
userSchema.index({ 'gameStats.rating': -1 });

// 密碼加密中間件
userSchema.pre('save', async function(next) {
  // 只有密碼被修改時才加密
  if (!this.isModified('password')) return next();

  try {
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS) || 12;
    this.password = await bcrypt.hash(this.password, saltRounds);
    next();
  } catch (error) {
    next(error);
  }
});

// 密碼驗證方法
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// 更新最後登入時間
userSchema.methods.updateLastLogin = function() {
  this.lastLogin = new Date();
  return this.save();
};

// 計算勝率
userSchema.virtual('gameStats.winRate').get(function() {
  if (this.gameStats.totalGames === 0) return 0;
  return (this.gameStats.wins / this.gameStats.totalGames * 100).toFixed(1);
});

// 獲取公開資料（不包含敏感資訊）
userSchema.methods.getPublicProfile = function() {
  return {
    id: this._id,
    username: this.username,
    profile: this.profile,
    gameStats: {
      totalGames: this.gameStats.totalGames,
      wins: this.gameStats.wins,
      losses: this.gameStats.losses,
      draws: this.gameStats.draws,
      rating: this.gameStats.rating,
      winRate: this.gameStats.winRate,
    },
    walletConnected: this.walletConnected,
    lastLogin: this.lastLogin,
    createdAt: this.createdAt,
  };
};

// 轉換為 JSON 時排除敏感資訊
userSchema.methods.toJSON = function() {
  const userObject = this.toObject();
  delete userObject.password;
  delete userObject.__v;
  return userObject;
};

module.exports = mongoose.model('User', userSchema);
