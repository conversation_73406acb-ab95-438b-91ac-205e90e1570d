// 認證功能測試
import { configureStore } from '@reduxjs/toolkit';
import authReducer, { 
  loginWithWallet, 
  loginWithCredentials, 
  registerUser,
  connectWallet,
  disconnectWallet,
  logout 
} from '../store/slices/authSlice';

// 模擬 API 服務
jest.mock('../services/api', () => ({
  apiService: {
    walletLogin: jest.fn(),
    login: jest.fn(),
    register: jest.fn(),
    post: jest.fn(),
  },
}));

// 模擬 Solana 服務
jest.mock('../services/solana', () => ({
  solanaService: {
    connectWallet: jest.fn(),
    signMessage: jest.fn(),
  },
}));

describe('Auth Slice', () => {
  let store;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        auth: authReducer,
      },
    });
  });

  describe('初始狀態', () => {
    test('應該有正確的初始狀態', () => {
      const state = store.getState().auth;
      expect(state).toEqual({
        isAuthenticated: false,
        token: null,
        walletAddress: null,
        user: null,
        loading: false,
        error: null,
      });
    });
  });

  describe('logout action', () => {
    test('應該清除所有認證資訊', () => {
      // 先設置一些認證狀態
      store.dispatch({
        type: 'auth/loginWithWallet/fulfilled',
        payload: {
          token: 'test-token',
          user: { id: '1', username: 'test' },
          walletAddress: 'test-address',
        },
      });

      // 執行登出
      store.dispatch(logout());

      const state = store.getState().auth;
      expect(state.isAuthenticated).toBe(false);
      expect(state.token).toBe(null);
      expect(state.walletAddress).toBe(null);
      expect(state.user).toBe(null);
      expect(state.error).toBe(null);
    });
  });

  describe('錢包登入', () => {
    test('pending 狀態應該設置 loading 為 true', () => {
      store.dispatch({
        type: loginWithWallet.pending.type,
      });

      const state = store.getState().auth;
      expect(state.loading).toBe(true);
      expect(state.error).toBe(null);
    });

    test('fulfilled 狀態應該設置認證資訊', () => {
      const payload = {
        token: 'test-token',
        user: { id: '1', username: 'testuser' },
        walletAddress: 'test-wallet-address',
      };

      store.dispatch({
        type: loginWithWallet.fulfilled.type,
        payload,
      });

      const state = store.getState().auth;
      expect(state.loading).toBe(false);
      expect(state.isAuthenticated).toBe(true);
      expect(state.token).toBe(payload.token);
      expect(state.user).toBe(payload.user);
      expect(state.walletAddress).toBe(payload.walletAddress);
      expect(state.error).toBe(null);
    });

    test('rejected 狀態應該設置錯誤訊息', () => {
      const errorMessage = '錢包連接失敗';

      store.dispatch({
        type: loginWithWallet.rejected.type,
        payload: errorMessage,
      });

      const state = store.getState().auth;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(errorMessage);
      expect(state.isAuthenticated).toBe(false);
    });
  });

  describe('傳統登入', () => {
    test('fulfilled 狀態應該設置認證資訊但不包含錢包地址', () => {
      const payload = {
        token: 'test-token',
        user: { id: '1', username: 'testuser' },
      };

      store.dispatch({
        type: loginWithCredentials.fulfilled.type,
        payload,
      });

      const state = store.getState().auth;
      expect(state.loading).toBe(false);
      expect(state.isAuthenticated).toBe(true);
      expect(state.token).toBe(payload.token);
      expect(state.user).toBe(payload.user);
      expect(state.walletAddress).toBe(null); // 傳統登入沒有錢包地址
      expect(state.error).toBe(null);
    });
  });

  describe('註冊', () => {
    test('fulfilled 狀態應該設置認證資訊', () => {
      const payload = {
        token: 'test-token',
        user: { id: '1', username: 'newuser' },
      };

      store.dispatch({
        type: registerUser.fulfilled.type,
        payload,
      });

      const state = store.getState().auth;
      expect(state.loading).toBe(false);
      expect(state.isAuthenticated).toBe(true);
      expect(state.token).toBe(payload.token);
      expect(state.user).toBe(payload.user);
      expect(state.walletAddress).toBe(null); // 註冊時沒有錢包地址
      expect(state.error).toBe(null);
    });
  });

  describe('錢包連接', () => {
    test('fulfilled 狀態應該更新錢包地址', () => {
      // 先設置已登入狀態
      store.dispatch({
        type: loginWithCredentials.fulfilled.type,
        payload: {
          token: 'test-token',
          user: { id: '1', username: 'testuser' },
        },
      });

      const payload = {
        user: { id: '1', username: 'testuser', walletConnected: true },
        walletAddress: 'new-wallet-address',
      };

      store.dispatch({
        type: connectWallet.fulfilled.type,
        payload,
      });

      const state = store.getState().auth;
      expect(state.loading).toBe(false);
      expect(state.walletAddress).toBe(payload.walletAddress);
      expect(state.user).toBe(payload.user);
      expect(state.error).toBe(null);
    });
  });

  describe('錢包斷開', () => {
    test('fulfilled 狀態應該清除錢包地址', () => {
      // 先設置已連接錢包的狀態
      store.dispatch({
        type: connectWallet.fulfilled.type,
        payload: {
          user: { id: '1', username: 'testuser', walletConnected: true },
          walletAddress: 'test-wallet-address',
        },
      });

      const payload = {
        user: { id: '1', username: 'testuser', walletConnected: false },
      };

      store.dispatch({
        type: disconnectWallet.fulfilled.type,
        payload,
      });

      const state = store.getState().auth;
      expect(state.loading).toBe(false);
      expect(state.walletAddress).toBe(null);
      expect(state.user).toBe(payload.user);
      expect(state.error).toBe(null);
    });
  });
});
